import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

class TestWorkflowRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, user_headers):
        self.client = test_client
        self.headers = user_headers

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_success(self, mock_workflow_service):
        # Arrange
        mock_workflow_service.return_value.create_workflow.return_value = {
            "id": "workflow_123",
            "status": "created"
        }

        workflow_data = {
            "name": "Test Workflow",
            "description": "Test workflow description",
            "steps": [{"id": "step1", "type": "task"}]
        }

        # Act
        response = self.client.post(
            "/api/v1/workflows",
            json=workflow_data,
            headers=self.headers
        )

        # Assert
        assert response.status_code == 200
        assert "id" in response.json()

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_get_workflow_status_success(self, mock_workflow_service):
        mock_workflow_service.return_value.get_workflow_status.return_value = {
            "id": "workflow_123",
            "status": "running"
        }

        response = self.client.get(
            "/api/v1/workflows/status/workflow_123",
            headers=self.headers
        )

        assert response.status_code == 200
        assert response.json()["status"] == "running"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_request_success(self, mock_workflow_service):
        workflow_data = {
            "workflow_id": "test_workflow",
            "payload": {
                "user_dependent_fields": ["field1", "field2"],
                "user_payload_template": {
                    "field1": "value1",
                    "field2": "value2"
                }
            }
        }

        response = self.client.post(
            "/api/v1/kafka/workflow-requests",
            json=workflow_data,
            headers=self.headers
        )

        assert response.status_code == 200

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_list_workflow_versions(self, mock_workflow_service):
        mock_workflow_service.return_value.list_workflow_versions.return_value = Mock(
            success=True,
            message="Listed versions",
            versions=[Mock(id="v1", version_number="1.0.0", is_current=True)],
            total=1,
            page=1,
            total_pages=1,
            current_version_id="v1"
        )
        response = self.client.get(
            "/api/v1/workflows/workflow_123/versions",
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["versions"][0]["id"] == "v1"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_get_workflow_version(self, mock_workflow_service):
        mock_workflow_service.return_value.get_workflow_version.return_value = Mock(
            success=True,
            message="Got version",
            version=Mock(id="v1", version_number="1.0.0", is_current=True)
        )
        response = self.client.get(
            "/api/v1/workflows/workflow_123/versions/v1",
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["id"] == "v1"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_switch_workflow_version(self, mock_workflow_service):
        mock_workflow_service.return_value.switch_workflow_version.return_value = Mock(
            success=True,
            message="Switched version",
            new_current_version=Mock(id="v2", version_number="2.0.0", is_current=True)
        )
        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions/v2/switch",
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["new_current_version"]["id"] == "v2"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_version(self, mock_workflow_service):
        mock_workflow_service.return_value.create_workflow_version.return_value = Mock(
            success=True,
            message="Created version",
            version=Mock(id="v3", version_number="3.0.0", is_current=False)
        )
        payload = {
            "workflow_id": "workflow_123",
            "user_id": "user_1",
            "name": "v3",
            "description": "desc",
            "changelog": "log",
            "auto_increment": True,
            "version_number": "3.0.0"
        }
        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions",
            json=payload,
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["id"] == "v3"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_list_workflow_versions_marketplace_user(self, mock_workflow_service):
        """Test listing workflow versions as a marketplace user for public workflow."""
        mock_workflow_service.return_value.list_workflow_versions.return_value = Mock(
            success=True,
            message="Listed versions for public workflow",
            versions=[
                Mock(id="v1", version_number="1.0.0", is_current=True, name="Public Version"),
                Mock(id="v2", version_number="2.0.0", is_current=False, name="Updated Public Version")
            ],
            total=2,
            page=1,
            total_pages=1,
            current_version_id="v1"
        )

        response = self.client.get(
            "/api/v1/workflows/public-workflow-123/versions",
            headers=self.headers
        )

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert len(response.json()["versions"]) == 2
        assert response.json()["versions"][0]["name"] == "Public Version"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_list_workflow_versions_permission_denied(self, mock_workflow_service):
        """Test listing workflow versions with permission denied for private workflow."""
        mock_workflow_service.return_value.list_workflow_versions.return_value = Mock(
            success=False,
            message="Permission denied: Cannot view versions of private workflow",
            versions=[],
            total=0,
            page=1,
            total_pages=0
        )

        response = self.client.get(
            "/api/v1/workflows/private-workflow-123/versions",
            headers=self.headers
        )

        assert response.status_code == 200  # API returns 200 but with success=False
        assert response.json()["success"] is False
        assert "Permission denied" in response.json()["message"]

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_get_workflow_version_marketplace_user(self, mock_workflow_service):
        """Test getting a specific workflow version as a marketplace user."""
        mock_workflow_service.return_value.get_workflow_version.return_value = Mock(
            success=True,
            message="Retrieved public version",
            version=Mock(
                id="v2",
                version_number="2.0.0",
                is_current=False,
                name="Public Version 2",
                description="Updated public workflow",
                changelog="Added new features for public use"
            )
        )

        response = self.client.get(
            "/api/v1/workflows/public-workflow-123/versions/v2",
            headers=self.headers
        )

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["version_number"] == "2.0.0"
        assert response.json()["version"]["name"] == "Public Version 2"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_switch_workflow_version_owner_only(self, mock_workflow_service):
        """Test switching workflow version - only owners should be able to do this."""
        mock_workflow_service.return_value.switch_workflow_version.return_value = Mock(
            success=True,
            message="Successfully switched to version 2.0.0",
            new_current_version=Mock(
                id="v2",
                version_number="2.0.0",
                is_current=True,
                name="Version 2"
            )
        )

        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions/v2/switch",
            headers=self.headers
        )

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert "Successfully switched" in response.json()["message"]
        assert response.json()["new_current_version"]["is_current"] is True

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_switch_workflow_version_permission_denied(self, mock_workflow_service):
        """Test switching workflow version with permission denied."""
        mock_workflow_service.return_value.switch_workflow_version.return_value = Mock(
            success=False,
            message="Permission denied: Only the workflow owner can switch versions"
        )

        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions/v2/switch",
            headers=self.headers
        )

        assert response.status_code == 200  # API returns 200 but with success=False
        assert response.json()["success"] is False
        assert "Permission denied" in response.json()["message"]

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_version_with_auto_increment(self, mock_workflow_service):
        """Test creating a new workflow version with auto-increment."""
        mock_workflow_service.return_value.create_workflow_version.return_value = Mock(
            success=True,
            message="Successfully created version 1.1.0",
            version=Mock(
                id="v_new",
                version_number="1.1.0",
                is_current=False,
                name="Auto-incremented Version",
                changelog="Automatically incremented version"
            )
        )

        payload = {
            "workflow_id": "workflow_123",
            "user_id": "user_1",
            "name": "Auto-incremented Version",
            "description": "New version with auto-increment",
            "changelog": "Automatically incremented version",
            "auto_increment": True,
            "version_number": ""  # Should be ignored when auto_increment is True
        }

        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions",
            json=payload,
            headers=self.headers
        )

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["version_number"] == "1.1.0"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_version_with_custom_version(self, mock_workflow_service):
        """Test creating a new workflow version with custom version number."""
        mock_workflow_service.return_value.create_workflow_version.return_value = Mock(
            success=True,
            message="Successfully created version 3.0.0",
            version=Mock(
                id="v_custom",
                version_number="3.0.0",
                is_current=False,
                name="Custom Version",
                changelog="Major version update"
            )
        )

        payload = {
            "workflow_id": "workflow_123",
            "user_id": "user_1",
            "name": "Custom Version",
            "description": "Major version update",
            "changelog": "Major version update with breaking changes",
            "auto_increment": False,
            "version_number": "3.0.0"
        }

        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions",
            json=payload,
            headers=self.headers
        )

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["version_number"] == "3.0.0"

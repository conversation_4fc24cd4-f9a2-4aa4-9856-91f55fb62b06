#!/usr/bin/env python3
"""
Simple test script to verify version management routes are properly defined.
This script tests the route definitions without requiring external dependencies.
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.getcwd())

def test_route_imports():
    """Test that we can import the version management routes."""
    try:
        from app.grpc_ import workflow_pb2, workflow_pb2_grpc
        print("✓ Successfully imported protobuf modules")
        
        # Test that version management messages exist
        assert hasattr(workflow_pb2, 'ListWorkflowVersionsRequest')
        assert hasattr(workflow_pb2, 'ListWorkflowVersionsResponse')
        assert hasattr(workflow_pb2, 'GetWorkflowVersionRequest')
        assert hasattr(workflow_pb2, 'GetWorkflowVersionResponse')
        assert hasattr(workflow_pb2, 'SwitchWorkflowVersionRequest')
        assert hasattr(workflow_pb2, 'SwitchWorkflowVersionResponse')
        assert hasattr(workflow_pb2, 'CreateWorkflowVersionRequest')
        assert hasattr(workflow_pb2, 'CreateWorkflowVersionResponse')
        assert hasattr(workflow_pb2, 'WorkflowVersion')
        print("✓ All version management protobuf messages are available")
        
        return True
    except Exception as e:
        print(f"✗ Failed to import protobuf modules: {e}")
        return False

def test_service_client():
    """Test that the workflow service client has version management methods."""
    try:
        from app.services.workflow_service import WorkflowServiceClient
        
        client = WorkflowServiceClient()
        
        # Check that version management methods exist
        assert hasattr(client, 'list_workflow_versions')
        assert hasattr(client, 'get_workflow_version')
        assert hasattr(client, 'switch_workflow_version')
        assert hasattr(client, 'create_workflow_version')
        print("✓ WorkflowServiceClient has all version management methods")
        
        return True
    except Exception as e:
        print(f"✗ Failed to test service client: {e}")
        return False

def test_route_definitions():
    """Test that the version management routes are defined."""
    try:
        # Import without initializing the full app to avoid Redis dependency
        import importlib.util
        
        # Load the workflow routes module
        spec = importlib.util.spec_from_file_location(
            "workflow_routes", 
            "app/api/routers/workflow_routes.py"
        )
        workflow_routes = importlib.util.module_from_spec(spec)
        
        # Read the file content to check for route definitions
        with open("app/api/routers/workflow_routes.py", "r") as f:
            content = f.read()
        
        # Check for version management route patterns
        version_routes = [
            "/{workflow_id}/versions",
            "/{workflow_id}/versions/{version_id}",
            "/{workflow_id}/versions/{version_id}/switch",
            "list_workflow_versions",
            "get_workflow_version", 
            "switch_workflow_version",
            "create_workflow_version"
        ]
        
        missing_routes = []
        for route in version_routes:
            if route not in content:
                missing_routes.append(route)
        
        if missing_routes:
            print(f"✗ Missing routes: {missing_routes}")
            return False
        else:
            print("✓ All version management routes are defined")
            return True
            
    except Exception as e:
        print(f"✗ Failed to test route definitions: {e}")
        return False

def test_schema_definitions():
    """Test that the version management schemas are defined."""
    try:
        # Read the schema file content
        with open("app/schemas/workflow.py", "r") as f:
            content = f.read()
        
        # Check for version management schema classes
        version_schemas = [
            "ListWorkflowVersionsRequest",
            "ListWorkflowVersionsResponse", 
            "GetWorkflowVersionRequest",
            "GetWorkflowVersionResponse",
            "SwitchWorkflowVersionRequest",
            "SwitchWorkflowVersionResponse",
            "CreateWorkflowVersionRequest",
            "CreateWorkflowVersionResponse",
            "WorkflowVersionInDB"
        ]
        
        missing_schemas = []
        for schema in version_schemas:
            if f"class {schema}" not in content:
                missing_schemas.append(schema)
        
        if missing_schemas:
            print(f"✗ Missing schemas: {missing_schemas}")
            return False
        else:
            print("✓ All version management schemas are defined")
            return True
            
    except Exception as e:
        print(f"✗ Failed to test schema definitions: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Version Management Implementation...")
    print("=" * 50)
    
    tests = [
        test_route_imports,
        test_service_client,
        test_route_definitions,
        test_schema_definitions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All version management components are properly implemented!")
        return 0
    else:
        print("❌ Some components need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())

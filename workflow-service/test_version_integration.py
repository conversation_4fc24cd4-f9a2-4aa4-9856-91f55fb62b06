#!/usr/bin/env python3
"""
Integration test for version management functionality.
Tests the complete flow from API request to database operations.
"""

import sys
import os
import uuid
from unittest.mock import Mock, patch, MagicMock

# Add the current directory to the path
sys.path.append(os.getcwd())

from app.grpc_ import workflow_pb2
from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow, WorkflowVersion
from app.utils.constants.constants import WorkflowVisibilityEnum


def test_version_management_flow():
    """Test the complete version management flow."""
    print("Testing Version Management Flow...")
    print("=" * 50)

    # Initialize the workflow functions
    workflow_functions = WorkflowFunctions()
    mock_context = Mock()

    # Test data
    workflow_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    marketplace_user_id = str(uuid.uuid4())
    version_id = str(uuid.uuid4())

    # Test 1: List Workflow Versions (Owner)
    print("Test 1: List Workflow Versions (Owner)")
    with patch('app.services.workflow_functions.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow
        mock_workflow = Mock()
        mock_workflow.id = workflow_id
        mock_workflow.owner_id = owner_id
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        mock_workflow.current_version_id = version_id

        # Mock version
        mock_version = Mock()
        mock_version.id = version_id
        mock_version.workflow_id = workflow_id
        mock_version.version_number = "1.0.0"
        mock_version.name = "Initial Version"
        mock_version.description = "First version"
        mock_version.workflow_url = "http://test.com/workflow"
        mock_version.builder_url = "http://test.com/builder"
        mock_version.start_nodes = []
        mock_version.category = "automation"
        mock_version.tags = []
        mock_version.changelog = "Initial release"
        mock_version.status = "active"
        mock_version.created_at = "2024-01-01T00:00:00"

        # Setup mock database responses
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_query = Mock()
        mock_query.count.return_value = 1
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_version]
        mock_db.query.return_value.filter.return_value = mock_query

        # Create request
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=workflow_id,
            user_id=owner_id,
            page=1,
            page_size=10
        )

        # Execute
        response = workflow_functions.listWorkflowVersions(request, mock_context)

        # Debug output
        print(f"Response success: {response.success}")
        print(f"Response message: {response.message}")

        # Verify
        assert response.success is True, f"Expected success=True, got success={response.success}, message={response.message}"
        assert response.total == 1
        assert len(response.versions) == 1
        assert response.versions[0].version_number == "1.0.0"
        print("✓ Owner can list workflow versions")

    # Test 2: List Workflow Versions (Marketplace User - Public Workflow)
    print("\nTest 2: List Workflow Versions (Marketplace User - Public Workflow)")
    with patch('app.services.workflow_functions.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock public workflow
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_db.query.return_value.filter.return_value = mock_query

        # Create request with marketplace user
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=workflow_id,
            user_id=marketplace_user_id,
            page=1,
            page_size=10
        )

        # Execute
        response = workflow_functions.listWorkflowVersions(request, mock_context)

        # Verify
        assert response.success is True
        assert response.total == 1
        print("✓ Marketplace user can list public workflow versions")

    # Test 3: List Workflow Versions (Marketplace User - Private Workflow - Denied)
    print("\nTest 3: List Workflow Versions (Marketplace User - Private Workflow - Denied)")
    with patch('app.services.workflow_functions.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock private workflow
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow

        # Create request with marketplace user
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=workflow_id,
            user_id=marketplace_user_id,
            page=1,
            page_size=10
        )

        # Execute
        response = workflow_functions.listWorkflowVersions(request, mock_context)

        # Verify
        assert response.success is False
        assert "Permission denied" in response.message
        print("✓ Marketplace user denied access to private workflow versions")

    # Test 4: Get Workflow Version
    print("\nTest 4: Get Workflow Version")
    with patch('app.services.workflow_functions.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow and version
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_db.query.return_value.filter.return_value.first.side_effect = [mock_workflow, mock_version]

        # Create request
        request = workflow_pb2.GetWorkflowVersionRequest(
            workflow_id=workflow_id,
            version_id=version_id,
            user_id=marketplace_user_id
        )

        # Execute
        response = workflow_functions.getWorkflowVersion(request, mock_context)

        # Verify
        assert response.success is True
        assert response.version.version_number == "1.0.0"
        print("✓ Can get specific workflow version")

    # Test 5: Switch Workflow Version (Owner Only)
    print("\nTest 5: Switch Workflow Version (Owner Only)")
    with patch('app.services.workflow_functions.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow and target version
        mock_workflow.current_version_id = "old-version-id"
        mock_target_version = Mock()
        mock_target_version.id = version_id
        mock_target_version.version_number = "2.0.0"
        mock_target_version.name = "Updated Version"
        mock_target_version.description = "Updated description"
        mock_target_version.workflow_url = "http://test.com/workflow-v2"
        mock_target_version.builder_url = "http://test.com/builder-v2"
        mock_target_version.start_nodes = []
        mock_target_version.category = "automation"
        mock_target_version.tags = []
        mock_target_version.changelog = "Version 2.0.0"
        mock_target_version.status = "active"
        mock_target_version.created_at = "2024-01-01T00:00:00"

        mock_db.query.return_value.filter.return_value.first.side_effect = [mock_workflow, mock_target_version]

        # Create request
        request = workflow_pb2.SwitchWorkflowVersionRequest(
            workflow_id=workflow_id,
            version_id=version_id,
            user_id=owner_id
        )

        # Execute
        response = workflow_functions.switchWorkflowVersion(request, mock_context)

        # Verify
        assert response.success is True
        assert "Successfully switched" in response.message
        print("✓ Owner can switch workflow versions")

    # Test 6: Create Workflow Version
    print("\nTest 6: Create Workflow Version")
    with patch('app.services.workflow_functions.SessionLocal') as mock_session:
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow and latest version
        mock_latest_version = Mock()
        mock_latest_version.version_number = "1.0.0"

        mock_db.query.return_value.filter.return_value.first.side_effect = [mock_workflow, mock_latest_version, None]
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = mock_latest_version

        # Create request
        request = workflow_pb2.CreateWorkflowVersionRequest(
            workflow_id=workflow_id,
            user_id=owner_id,
            name="New Version",
            description="New version description",
            changelog="Added new features",
            auto_increment=True
        )

        # Execute
        response = workflow_functions.createWorkflowVersion(request, mock_context)

        # Verify
        assert response.success is True
        assert "Successfully created" in response.message
        print("✓ Owner can create new workflow versions")

    print("\n" + "=" * 50)
    print("🎉 All version management tests passed!")
    print("✅ Implementation supports both owners and marketplace users")
    print("✅ Proper permission checking implemented")
    print("✅ All CRUD operations working correctly")
    return True


def main():
    """Run the integration test."""
    try:
        success = test_version_management_flow()
        if success:
            print("\n🚀 Version Management Implementation is Ready!")
            return 0
        else:
            print("\n❌ Some tests failed")
            return 1
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

import grpc
import os
import sys
import pytest
import uuid
from unittest.mock import Mock, patch

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow, WorkflowVersion
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum


class TestVersionManagement:
    """Test suite for workflow version management functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.workflow_functions = WorkflowFunctions()
        self.mock_context = Mock()

        # Sample workflow data
        self.sample_workflow_id = str(uuid.uuid4())
        self.sample_user_id = str(uuid.uuid4())
        self.sample_version_id = str(uuid.uuid4())

    @patch('app.services.workflow_functions.SessionLocal')
    def test_list_workflow_versions_owner_success(self, mock_session):
        """Test listing workflow versions as the owner."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        mock_workflow.current_version_id = self.sample_version_id

        # Mock versions
        mock_version = Mock()
        mock_version.id = self.sample_version_id
        mock_version.workflow_id = self.sample_workflow_id
        mock_version.version_number = "1.0.0"
        mock_version.name = "Test Version"
        mock_version.description = "Test Description"
        mock_version.workflow_url = "http://test.com/workflow"
        mock_version.builder_url = "http://test.com/builder"
        mock_version.start_nodes = []
        mock_version.category = "automation"
        mock_version.tags = []
        mock_version.changelog = "Initial version"
        mock_version.status = "active"
        mock_version.created_at = "2024-01-01T00:00:00"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_query = Mock()
        mock_query.count.return_value = 1
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_version]
        mock_db.query.return_value.filter.return_value = mock_query

        # Create request
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=self.sample_workflow_id,
            user_id=self.sample_user_id,
            page=1,
            page_size=10
        )

        # Act
        response = self.workflow_functions.listWorkflowVersions(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.total == 1
        assert len(response.versions) == 1
        assert response.versions[0].version_number == "1.0.0"
        assert response.current_version_id == self.sample_version_id

    @patch('app.services.workflow_functions.SessionLocal')
    def test_list_workflow_versions_marketplace_user_public_workflow(self, mock_session):
        """Test listing workflow versions as a marketplace user for public workflow."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        different_user_id = str(uuid.uuid4())

        # Mock public workflow
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id  # Different from requesting user
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_workflow.current_version_id = self.sample_version_id

        # Mock version
        mock_version = Mock()
        mock_version.id = self.sample_version_id
        mock_version.workflow_id = self.sample_workflow_id
        mock_version.version_number = "1.0.0"
        mock_version.name = "Public Test Version"
        mock_version.description = "Public Test Description"
        mock_version.workflow_url = "http://test.com/workflow"
        mock_version.builder_url = "http://test.com/builder"
        mock_version.start_nodes = []
        mock_version.category = "automation"
        mock_version.tags = []
        mock_version.changelog = "Initial version"
        mock_version.status = "active"
        mock_version.created_at = "2024-01-01T00:00:00"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_query = Mock()
        mock_query.count.return_value = 1
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_version]
        mock_db.query.return_value.filter.return_value = mock_query

        # Create request with different user
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=self.sample_workflow_id,
            user_id=different_user_id,
            page=1,
            page_size=10
        )

        # Act
        response = self.workflow_functions.listWorkflowVersions(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.total == 1
        assert len(response.versions) == 1

    @patch('app.services.workflow_functions.SessionLocal')
    def test_list_workflow_versions_marketplace_user_private_workflow_denied(self, mock_session):
        """Test listing workflow versions as a marketplace user for private workflow - should be denied."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        different_user_id = str(uuid.uuid4())

        # Mock private workflow
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id  # Different from requesting user
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow

        # Create request with different user
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=self.sample_workflow_id,
            user_id=different_user_id,
            page=1,
            page_size=10
        )

        # Act
        response = self.workflow_functions.listWorkflowVersions(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Permission denied" in response.message
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    @patch('app.services.workflow_functions.SessionLocal')
    def test_list_workflow_versions_workflow_not_found(self, mock_session):
        """Test listing workflow versions for non-existent workflow."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock database queries - workflow not found
        mock_db.query.return_value.filter.return_value.first.return_value = None

        # Create request
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id=self.sample_workflow_id,
            user_id=self.sample_user_id,
            page=1,
            page_size=10
        )

        # Act
        response = self.workflow_functions.listWorkflowVersions(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Workflow not found" in response.message
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    @patch('app.services.workflow_functions.SessionLocal')
    def test_get_workflow_version_success(self, mock_session):
        """Test getting a specific workflow version successfully."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_workflow.current_version_id = self.sample_version_id

        # Mock version
        mock_version = Mock()
        mock_version.id = self.sample_version_id
        mock_version.workflow_id = self.sample_workflow_id
        mock_version.version_number = "1.0.0"
        mock_version.name = "Test Version"
        mock_version.description = "Test Description"
        mock_version.workflow_url = "http://test.com/workflow"
        mock_version.builder_url = "http://test.com/builder"
        mock_version.start_nodes = []
        mock_version.category = "automation"
        mock_version.tags = []
        mock_version.changelog = "Initial version"
        mock_version.status = "active"
        mock_version.created_at = "2024-01-01T00:00:00"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [mock_workflow, mock_version]

        # Create request
        request = workflow_pb2.GetWorkflowVersionRequest(
            workflow_id=self.sample_workflow_id,
            version_id=self.sample_version_id,
            user_id=self.sample_user_id
        )

        # Act
        response = self.workflow_functions.getWorkflowVersion(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.version.version_number == "1.0.0"
        assert response.version.is_current is True

    @patch('app.services.workflow_functions.SessionLocal')
    def test_switch_workflow_version_success(self, mock_session):
        """Test switching workflow version successfully."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id
        mock_workflow.current_version_id = "old-version-id"

        # Mock target version
        mock_target_version = Mock()
        mock_target_version.id = self.sample_version_id
        mock_target_version.workflow_id = self.sample_workflow_id
        mock_target_version.version_number = "2.0.0"
        mock_target_version.name = "New Version"
        mock_target_version.description = "New Description"
        mock_target_version.workflow_url = "http://test.com/workflow-v2"
        mock_target_version.builder_url = "http://test.com/builder-v2"
        mock_target_version.start_nodes = []
        mock_target_version.category = "automation"
        mock_target_version.tags = []
        mock_target_version.changelog = "Version 2.0.0"
        mock_target_version.status = "active"
        mock_target_version.created_at = "2024-01-01T00:00:00"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [mock_workflow, mock_target_version]

        # Create request
        request = workflow_pb2.SwitchWorkflowVersionRequest(
            workflow_id=self.sample_workflow_id,
            version_id=self.sample_version_id,
            user_id=self.sample_user_id
        )

        # Act
        response = self.workflow_functions.switchWorkflowVersion(request, self.mock_context)

        # Assert
        assert response.success is True
        assert "Successfully switched" in response.message
        assert response.new_current_version.version_number == "2.0.0"
        mock_db.commit.assert_called_once()

    @patch('app.services.workflow_functions.SessionLocal')
    def test_switch_workflow_version_permission_denied(self, mock_session):
        """Test switching workflow version with permission denied."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        different_user_id = str(uuid.uuid4())

        # Mock workflow with different owner
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id  # Different from requesting user

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow

        # Create request with different user
        request = workflow_pb2.SwitchWorkflowVersionRequest(
            workflow_id=self.sample_workflow_id,
            version_id=self.sample_version_id,
            user_id=different_user_id
        )

        # Act
        response = self.workflow_functions.switchWorkflowVersion(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Permission denied" in response.message
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    @patch('app.services.workflow_functions.SessionLocal')
    def test_create_workflow_version_success(self, mock_session):
        """Test creating a new workflow version successfully."""
        # Arrange
        mock_db = Mock()
        mock_session.return_value = mock_db

        # Mock workflow
        mock_workflow = Mock()
        mock_workflow.id = self.sample_workflow_id
        mock_workflow.owner_id = self.sample_user_id
        mock_workflow.name = "Test Workflow"
        mock_workflow.description = "Test Description"
        mock_workflow.workflow_url = "http://test.com/workflow"
        mock_workflow.builder_url = "http://test.com/builder"
        mock_workflow.start_nodes = []
        mock_workflow.category = "automation"
        mock_workflow.tags = []

        # Mock latest version for auto-increment
        mock_latest_version = Mock()
        mock_latest_version.version_number = "1.0.0"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [mock_workflow, mock_latest_version, None]  # workflow, latest_version, existing_version
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = mock_latest_version

        # Create request
        request = workflow_pb2.CreateWorkflowVersionRequest(
            workflow_id=self.sample_workflow_id,
            user_id=self.sample_user_id,
            name="New Version",
            description="New version description",
            changelog="Added new features",
            auto_increment=True
        )

        # Act
        response = self.workflow_functions.createWorkflowVersion(request, self.mock_context)

        # Assert
        assert response.success is True
        assert "Successfully created" in response.message
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()


def test_list_workflow_versions_integration():
    """Integration test for listing workflow versions via gRPC."""
    # This test requires a running gRPC server
    try:
        # Connect to the gRPC server
        channel = grpc.insecure_channel("localhost:50056")
        stub = workflow_pb2_grpc.WorkflowServiceStub(channel)

        # Create a test request
        request = workflow_pb2.ListWorkflowVersionsRequest(
            workflow_id="test-workflow-id",
            user_id="test-user-id",
            page=1,
            page_size=10
        )

        # Call the service
        response = stub.listWorkflowVersions(request)

        # Basic assertions
        assert hasattr(response, 'success')
        assert hasattr(response, 'message')
        assert hasattr(response, 'versions')

        print(f"Integration test response: {response.success}, {response.message}")

    except grpc.RpcError as e:
        print(f"gRPC Error (expected if server not running): {e.code()}: {e.details()}")
    except Exception as e:
        print(f"Connection error (expected if server not running): {e}")


if __name__ == "__main__":
    print("Running version management tests...")

    # Run unit tests
    test_instance = TestVersionManagement()
    test_instance.setup_method()

    try:
        test_instance.test_list_workflow_versions_owner_success()
        print("✓ test_list_workflow_versions_owner_success passed")
    except Exception as e:
        print(f"✗ test_list_workflow_versions_owner_success failed: {e}")

    try:
        test_instance.test_list_workflow_versions_marketplace_user_public_workflow()
        print("✓ test_list_workflow_versions_marketplace_user_public_workflow passed")
    except Exception as e:
        print(f"✗ test_list_workflow_versions_marketplace_user_public_workflow failed: {e}")

    try:
        test_instance.test_list_workflow_versions_marketplace_user_private_workflow_denied()
        print("✓ test_list_workflow_versions_marketplace_user_private_workflow_denied passed")
    except Exception as e:
        print(f"✗ test_list_workflow_versions_marketplace_user_private_workflow_denied failed: {e}")

    try:
        test_instance.test_list_workflow_versions_workflow_not_found()
        print("✓ test_list_workflow_versions_workflow_not_found passed")
    except Exception as e:
        print(f"✗ test_list_workflow_versions_workflow_not_found failed: {e}")

    # Run integration test
    test_list_workflow_versions_integration()

    print("Version management tests completed!")
